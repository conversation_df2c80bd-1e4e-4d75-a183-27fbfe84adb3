import axiosInstance from '../../axiosConfig';

// Product interfaces - Updated to match actual API response
export interface Product {
  PRDNO: string;
  DESCP: string;
  ItemID: number;
  SkuName: string;
  AssociatedItem: string;
  BrandName: string;
  REUPC: string;
  GRUPC: string;
  // Legacy fields for backward compatibility
  Brand?: string;
  SalesDesc?: string;
  [key: string]: any; // Allow for additional dynamic fields
}

export interface ProductSearchParams {
  page?: number;
  limit?: number;
  PRDNO?: string;
  PRMSDescription?: string;
  RetailUPC?: string;
  BrandName?: string;
  SkuName?: string;
  AssociatedItem?: string;
  search?: string; // Generic search parameter
}

export interface ProductSearchResponse {
  products: Product[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface ListItem {
  value: string;
  label: string;
}

// The API returns an array of strings, not objects
export type ListItemsResponse = string[];

// Product mapping details interface
export interface ProductMappingDetails {
  VCID: number;
  PRDNO: string;
  FromPRDNO: string | null;
  ToPRDNO: string | null;
  DataSource: string;
  DistItemID: string;
  DistItemNum: string;
  DistItemUPC: string;
  DistItemName: string;
  LastTransactionDate: string;
  L4POSSales: number;
  IsPending: boolean;
  MappingStatus: string | null;
}

// Product mapping suggestions interface
export interface ProductMappingSuggestion {
  DataSource: string;
  PRDNO: string;
  DESCP: string;
  REUPC: string;
  GRUPC: string;
  XUFLD1: string;
  ItemID: number;
  SkuName: string;
  BrandName: string;
  AssociatedItem: string;
}

export interface ProductMappingSuggestionsResponse {
  suggestions: ProductMappingSuggestion[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

// Product mapping suggestions search parameters
export interface ProductMappingSuggestionsParams {
  VCID: number;
  BrandName?: string;
  SkuName?: string;
  PRMSDescription?: string;
  RetailUPC?: string;
  page?: number;
  limit?: number;
}

// Product API service class
class ProductService {
  private baseUrl = '/products';

  /**
   * Get list items for dropdowns (brands, etc.)
   */
  async getListItems(): Promise<string[]> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/list-items`);
      return response.data;
    } catch (error) {
      console.error('Error fetching list items:', error);
      throw new Error('Failed to fetch list items');
    }
  }

  /**
   * Search products with filters and pagination
   */
  async searchProducts(params: ProductSearchParams): Promise<ProductSearchResponse> {
    try {
      // Clean up empty parameters
      const cleanParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const response = await axiosInstance.get(`${this.baseUrl}/search`, {
        params: cleanParams,
      });
      
      return response.data;
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(id: string): Promise<Product> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }

  /**
   * Get product mapping details by VCID
   */
  async getProductMappingDetails(vcid: number): Promise<ProductMappingDetails> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/mapping-details/${vcid}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product mapping details:', error);
      throw new Error('Failed to fetch product mapping details');
    }
  }

  /**
   * Get product mapping suggestions
   */
  async getProductMappingSuggestions(params: ProductMappingSuggestionsParams): Promise<ProductMappingSuggestionsResponse> {
    try {
      // Clean up empty parameters
      const cleanParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const response = await axiosInstance.get(`${this.baseUrl}/mapping-suggestions`, {
        params: cleanParams,
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching product mapping suggestions:', error);
      throw new Error('Failed to fetch product mapping suggestions');
    }
  }
}

// Export singleton instance
export const productService = new ProductService();
export default productService;
