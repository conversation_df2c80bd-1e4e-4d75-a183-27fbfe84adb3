import axiosInstance from '../../axiosConfig';

// Product interfaces - Updated to match actual API response
export interface Product {
  PRDNO: string;
  DESCP: string;
  ItemID: number;
  SkuName: string;
  AssociatedItem: string;
  BrandName: string;
  REUPC: string;
  GRUPC: string;
  // Legacy fields for backward compatibility
  Brand?: string;
  SalesDesc?: string;
  [key: string]: any; // Allow for additional dynamic fields
}

export interface ProductSearchParams {
  page?: number;
  limit?: number;
  PRDNO?: string;
  PRMSDescription?: string;
  RetailUPC?: string;
  BrandName?: string;
  SkuName?: string;
  AssociatedItem?: string;
  search?: string; // Generic search parameter
}

export interface ProductSearchResponse {
  products: Product[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface ListItem {
  value: string;
  label: string;
}

// The API returns an array of strings, not objects
export type ListItemsResponse = string[];

// Product mapping details interface - Updated with new API response structure
export interface ProductMappingDetails {
  DataSource: string;
  DistItemName: string;
  DistItemID: string;
  DistItemUPC: string;
  TransactionDateRange: string;
  InventoryOnHand: number;
  InventoryLastDate: string;
  L4WeeksQty: number;
  L4WeeksPOSAmount: number;
  L4WeeksPOSPricePerEA: number;
  L4WeeksGSVAmount: number;
  L4WeeksGSVPricePerEA: number;
  L52WeeksQty: number;
  L52WeeksPOSAmount: number;
  L52WeeksPOSPricePerEA: number;
  L52WeeksGSVAmount: number;
  L52WeeksGSVPricePerEA: number;
  AllTimeQty: number;
  AllTimePOSAmount: number;
  AllTimePOSPricePerEA: number;
  AllTimeGSVAmount: number;
  AllTimeGSVPricePerEA: number;
  MappedToPRDNO: string | null;
  MappedToDescription: string | null;
  IsPending: boolean;
  "remove Insufficient Item ID/UPC": boolean;
  "Insufficient Item Name": boolean;
}

// Product mapping suggestions interface
export interface ProductMappingSuggestion {
  DataSource: string;
  PRDNO: string;
  DESCP: string;
  REUPC: string;
  GRUPC: string;
  XUFLD1: string;
  ItemID: number;
  SkuName: string;
  BrandName: string;
  AssociatedItem: string;
}

export interface ProductMappingSuggestionsResponse {
  suggestions: ProductMappingSuggestion[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

// Product mapping suggestions search parameters
export interface ProductMappingSuggestionsParams {
  VCID: number;
  BrandName?: string;
  SkuName?: string;
  PRMSDescription?: string;
  RetailUPC?: string;
  page?: number;
  limit?: number;
}

// Product mapping update interfaces
export interface ProductMappingUpdateParams {
  VCID: string;
  PRDNO: string;
  MappingUser: string;
}

export interface ProductMappingUpdateResponse {
  status: string;
  message: string;
  parameters: {
    VCID: number;
    PRDNO: string;
    MappingUser: string;
    UOMPK_Override: string | null;
    UOMEA_Override: string | null;
    BadDistItemID: string | null;
    BadDistItemName: string | null;
  };
}

// Product API service class
class ProductService {
  private baseUrl = '/products';

  /**
   * Get list items for dropdowns (brands, etc.)
   */
  async getListItems(): Promise<string[]> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/list-items`);
      return response.data;
    } catch (error) {
      console.error('Error fetching list items:', error);
      throw new Error('Failed to fetch list items');
    }
  }

  /**
   * Search products with filters and pagination
   */
  async searchProducts(params: ProductSearchParams): Promise<ProductSearchResponse> {
    try {
      // Clean up empty parameters
      const cleanParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const response = await axiosInstance.get(`${this.baseUrl}/search`, {
        params: cleanParams,
      });
      
      return response.data;
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(id: string): Promise<Product> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }

  /**
   * Get product mapping details by VCID
   */
  async getProductMappingDetails(vcid: number): Promise<ProductMappingDetails> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/mapping-details/${vcid}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product mapping details:', error);
      throw new Error('Failed to fetch product mapping details');
    }
  }

  /**
   * Get product mapping suggestions
   */
  async getProductMappingSuggestions(params: ProductMappingSuggestionsParams): Promise<ProductMappingSuggestionsResponse> {
    try {
      // Clean up empty parameters
      const cleanParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const response = await axiosInstance.get(`${this.baseUrl}/mapping-suggestions`, {
        params: cleanParams,
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching product mapping suggestions:', error);
      throw new Error('Failed to fetch product mapping suggestions');
    }
  }

  /**
   * Update product mapping
   */
  async updateProductMapping(params: ProductMappingUpdateParams): Promise<ProductMappingUpdateResponse> {
    try {
      const response = await axiosInstance.post(`/product-mappings/update`, params);
      return response.data;
    } catch (error) {
      console.error('Error updating product mapping:', error);
      throw new Error('Failed to update product mapping');
    }
  }
}

// Export singleton instance
export const productService = new ProductService();
export default productService;
