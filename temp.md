why is it that after long detailed description I still see issues/bugs after you implement them, please please remember to implement things the best way.
think harder, implement things the best way.
-------------------------------------------------
pls solve the below issues:

issue 1:
design across the application is not uniform, right component having tabs in store-desc/ page are having select buttons in action tab, whereas we are having radio buttons in product-desc page. change the implementation of radio buttons in product-desc page to match with the implementation of select buttons in store-desc/page

issue 2:
save and cancel buttons should only be active when user has selected a record in `suggestions` tab or `find all` tab of product-desc page, in all else cases, they should be disabled (grayed out).

issue 3:
delete button in product-desc should be active if `Mapping Status` is having some text (not null or None or '-' or empty string) as it's value

issue 4:
the tabular data in `Product Details` component if has zero's should display zeros, it should display '-' for non descriptive values such as null or None

issue 5:
upon loading product-desc page, BE APIs products/mapping-suggestions, products/mapping-details are being called twice, this leads to bad UX, and probable performance issues, fix this issue by calling these two apis once upon loading product-desc page.

issue 6:
BE API /product-mappings/update is to be replaced by /products/product-mappings/update, upon clicking save button in the `suggestions` tab or `find all` tab of product-desc page make sure to reload the `Product Details` component, make sure to call the api and reload the component.
------------------------------------------
pls do the below enhancements:
enhancement 1:
remove edit button from product-desc page.