// components/MappedProductSearch.tsx
import React, { useState, useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box } from "@mui/material";
import FilterRow from "../../components/FilterRow";
import { debounce } from "@mui/material/utils";
import { RootState } from "../../store";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";
import { searchMappedItems } from "../../services/action/mapping.action";
import { setMappedSearchQuery, setMappedFilters, resetMappedFilters } from "../../store/slice/mappingSlice";

// Type alias for dispatch
type AppDispatch = ThunkDispatch<RootState, any, UnknownAction>;

const MappedProductSearch = () => {
  const dispatch: AppDispatch = useDispatch();
  const navigate = useNavigate();
  
  // Get state from Redux store
  const { 
    mappedItems, 
    mappedItemsLoading, 
    mappedItemsError,
    mappedItemsTotalCount,
    mappedSearchQuery,
    mappedFilters 
  } = useSelector((state: RootState) => state.mapping);

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);

  // Reset filters when component mounts
  useEffect(() => {
    dispatch(resetMappedFilters());
  }, [dispatch]);

  // Fetch data function for search query (no filter dependencies)
  const fetchMappedItemsForSearch = useCallback(() => {
    const params = {
      page: pagination.page,
      limit: pagination.rowsPerPage,
      search_query: mappedSearchQuery || undefined,
    };

    dispatch(searchMappedItems(params));
  }, [dispatch, pagination.page, pagination.rowsPerPage, mappedSearchQuery]);

  // Fetch data function for filters (called only on apply)
  const fetchMappedItemsWithFilters = useCallback(() => {
    const params = {
      page: pagination.page,
      limit: pagination.rowsPerPage,
      search_query: mappedSearchQuery || undefined,
      data_source: mappedFilters.data_source || undefined,
      dist_item_id: mappedFilters.dist_item_id || undefined,
      dist_item_upc: mappedFilters.dist_item_upc || undefined,
      dist_item_name: mappedFilters.dist_item_name || undefined,
      prdno: mappedFilters.prdno || undefined,
      descp: mappedFilters.descp || undefined,
    };

    dispatch(searchMappedItems(params));
  }, [dispatch, pagination.page, pagination.rowsPerPage, mappedSearchQuery, mappedFilters]);

  // Debounced search handler (4 seconds delay as per user preference)
  const handleSearch = useCallback(
    debounce((query: string) => {
      dispatch(setMappedSearchQuery(query));
      setPagination((prev) => ({ ...prev, page: 1 }));
      setIsFilterApplied(true);
    }, 4000),
    [dispatch],
  );

  // Filter change handler
  const handleFilterChange = (name: string, value: string) => {
    dispatch(setMappedFilters({ [name]: value }));
  };

  // Apply filters handler
  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(true);
    // Remove direct API call - let useEffect handle it to avoid double calls
  };

  // Reset filters handler
  const handleResetFilters = () => {
    dispatch(resetMappedFilters());
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(false);
  };

  // Pagination handler
  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  // Handle hyperlink click for DistItemName column
  const handleDistItemNameClick = (prdno: string, vcid: number) => {
    navigate(`/product-desc/id=${prdno}&VCID=${vcid}`);
  };

  // Handle hyperlink click for Mapped To column
  const handleMappedToClick = (prdno: string) => {
    navigate(`/products/details?id=${prdno}`);
  };

  // Fetch data when search query changes (debounced search)
  useEffect(() => {
    if (mappedSearchQuery) {
      fetchMappedItemsForSearch();
    }
  }, [mappedSearchQuery, pagination.page, pagination.rowsPerPage, fetchMappedItemsForSearch]);

  // Fetch data when pagination changes and filters are applied
  useEffect(() => {
    if (isFilterApplied && !mappedSearchQuery) {
      fetchMappedItemsWithFilters();
    }
  }, [pagination.page, pagination.rowsPerPage, isFilterApplied, mappedSearchQuery]);

  // Filter configurations for mapped product search
  const filterConfigs = [
    {
      name: "data_source",
      label: "Data Source",
      type: "text" as const,
      value: mappedFilters.data_source,
      onChange: (value: string) => handleFilterChange("data_source", value),
    },
    {
      name: "dist_item_id",
      label: "DistItemID",
      type: "text" as const,
      value: mappedFilters.dist_item_id,
      onChange: (value: string) => handleFilterChange("dist_item_id", value),
    },
    {
      name: "dist_item_upc",
      label: "DistItemUPC",
      type: "text" as const,
      value: mappedFilters.dist_item_upc,
      onChange: (value: string) => handleFilterChange("dist_item_upc", value),
    },
    {
      name: "dist_item_name",
      label: "DistItemName",
      type: "text" as const,
      value: mappedFilters.dist_item_name,
      onChange: (value: string) => handleFilterChange("dist_item_name", value),
    },
    {
      name: "prdno",
      label: "PRDNO",
      type: "text" as const,
      value: mappedFilters.prdno,
      onChange: (value: string) => handleFilterChange("prdno", value),
    },
    {
      name: "descp",
      label: "DESCP",
      type: "text" as const,
      value: mappedFilters.descp,
      onChange: (value: string) => handleFilterChange("descp", value),
    }
  ];

  // Table columns matching the API response structure
  const columns = [
    {
      id: "DataSource",
      label: "DataSource",
      description: "Data source identifier",
    },
    {
      id: "DistItemID",
      label: "Item ID",
      description: "Item identifier",
    },
    {
      id: "DistItemUPC",
      label: "Item UPC",
      description: "Item UPC code",
    },
    {
      id: "DistItemName",
      label: "Dist Item Desc",
      description: "Distribution item description",
      format: (value: string, row: any) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => handleDistItemNameClick(row.PRDNO, row.VCID)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "LastTransactionDate",
      label: "Last Dt",
      description: "Last transaction date",
      format: (value: string | null) => value ? new Date(value).toLocaleDateString() : "-",
    },
    {
      id: "L4POSSales",
      label: "Last 4 POS",
      description: "Last 4 POS sales",
      format: (value: number) => value.toString(),
    },
    {
      id: "Last52POS",
      label: "Last 52 POS",
      description: "Last 52 POS sales",
      format: () => "-", // Not available in API response
    },
    {
      id: "TotalPOSSales",
      label: "TotalPOSSales",
      description: "Total POS sales",
      format: () => "-", // Not available in API response
    },
    {
      id: "POSPr",
      label: "POS Pr",
      description: "POS Price",
      format: () => "-", // Not available in API response
    },
    {
      id: "PRMSPr",
      label: "PRMS Pr",
      description: "PRMS Price",
      format: () => "-", // Not available in API response
    },
    {
      id: "InvOH",
      label: "Inv OH",
      description: "Inventory On Hand",
      format: () => "-", // Not available in API response
    },
    {
      id: "DESCP",
      label: "Mapped To",
      description: "Mapped to description",
      format: (value: string, row: any) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => handleMappedToClick(row.PRDNO)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "Brand",
      label: "Brand",
      description: "Brand name",
      format: () => "-", // Not available in API response
    },
    {
      id: "MappingStatus",
      label: "IsPending",
      description: "Mapping status",
    },
  ];

  return (
    <div>
      {/* TopBarLayout with breadcrumb and search */}
      <TopBarLayout
        breadcrumbItems={["Product", "Mapped Search"]}
        onSearchChange={handleSearch}
      />

      {/* FilterRow with mapped product search fields */}
      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {/* Table component - only show when filters are applied */}
      {isFilterApplied && (
        <TableComponent
          columns={columns}
          rows={mappedItems}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={mappedItemsTotalCount}
          onPageChange={handlePageChange}
          showActions={false}
          isLoading={mappedItemsLoading}
        />
      )}

      {/* Show error message if there's an error */}
      {mappedItemsError && (
        <Box sx={{ p: 3, textAlign: "center", color: "error.main" }}>
          Error: {mappedItemsError}
        </Box>
      )}
    </div>
  );
};

export default MappedProductSearch;
