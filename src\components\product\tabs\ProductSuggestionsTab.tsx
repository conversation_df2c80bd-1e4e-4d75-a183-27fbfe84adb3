import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { Box, Radio, CircularProgress } from "@mui/material";
import TableComponent from "../../TableComponent";
import { fetchProductMappingSuggestions } from "../../../services/action/product.action";
import { ThunkDispatch } from "redux-thunk";
import { RootState } from "../../../store";
import { UnknownAction } from "@reduxjs/toolkit";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

interface ProductSuggestion {
  DataSource: string;
  PRDNO: string;
  DESCP: string;
  REUPC: string;
  GRUPC: string;
  XUFLD1: string;
  ItemID: number;
  SkuName: string;
  BrandName: string;
  AssociatedItem: string;
}

interface ProductSuggestionsResponse {
  suggestions: ProductSuggestion[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

interface ProductSuggestionsTabProps {
  vcid: string;
  onMappedToClick: (product: ProductSuggestion) => void;
  onProductSelect: (product: { VCID: number; PRDNO: string }) => void;
  selectedProduct: { VCID: number; PRDNO: string } | null;
  pagination: {
    page: number;
    rowsPerPage: number;
  };
  onPageChange: (newPage: number, newRowsPerPage: number) => void;
}

const ProductSuggestionsTab: React.FC<ProductSuggestionsTabProps> = ({
  vcid,
  onMappedToClick,
  onProductSelect,
  selectedProduct,
  pagination,
  onPageChange,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const [suggestions, setSuggestions] = useState<ProductSuggestion[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch suggestions data
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (vcid) {
        setIsLoading(true);
        setError(null);

        try {
          const params = {
            VCID: parseInt(vcid),
            page: pagination.page,
            limit: pagination.rowsPerPage,
          };

          const result = await dispatch(fetchProductMappingSuggestions(params));

          if (fetchProductMappingSuggestions.fulfilled.match(result)) {
            setSuggestions(result.payload.suggestions);
            setTotalCount(result.payload.total_count);
          } else {
            setError(result.payload || "Failed to fetch product suggestions");
          }
        } catch (err) {
          setError("Failed to fetch product suggestions");
          console.error("Error fetching suggestions:", err);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchSuggestions();
  }, [vcid, pagination.page, pagination.rowsPerPage, dispatch]);

  const handleRadioChange = (product: ProductSuggestion) => {
    onProductSelect({
      VCID: parseInt(vcid),
      PRDNO: product.PRDNO,
    });
  };

  const columns = [
    {
      id: "action",
      label: "Action",
      description: "Select product",
      format: (value: any, row: ProductSuggestion) => (
        <Radio
          checked={selectedProduct?.PRDNO === row.PRDNO}
          onChange={() => handleRadioChange(row)}
          value={row.PRDNO}
          name="product-selection"
        />
      ),
    },
    {
      id: "PRDNO",
      label: "PRDNO",
      description: "Product number",
      format: (value: string, row: ProductSuggestion) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => onMappedToClick(row)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "DESCP",
      label: "DESCP",
      description: "Product description",
    },
    {
      id: "REUPC",
      label: "REUPC",
      description: "Retail UPC",
    },
    {
      id: "ItemID",
      label: "ItemID",
      description: "Item identifier",
    },
    {
      id: "SkuName",
      label: "SkuName",
      description: "SKU name",
    },
    {
      id: "AssociatedItem",
      label: "AssociatedItem",
      description: "Associated item",
    },
  ];

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: "center", color: "error.main" }}>
        {error}
      </Box>
    );
  }

  return (
    <Box>
      {isLoading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
          <CircularProgress />
        </Box>
      ) : (
        <TableComponent
          columns={columns}
          rows={suggestions}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={totalCount}
          onPageChange={onPageChange}
          showActions={false}
          isLoading={isLoading}
        />
      )}
    </Box>
  );
};

export default ProductSuggestionsTab;
