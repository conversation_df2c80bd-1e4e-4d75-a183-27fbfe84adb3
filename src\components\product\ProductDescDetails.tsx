import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import TopBarLayout from "../TopBarLayout";
import { updateProductMapping } from "../../services/action/product.action";
import {
  Box,
  Paper,
  Typography,
  Divider,
  Grid,
  Tabs,
  Tab,
  Button,
  CircularProgress,
} from "@mui/material";
import ProductInfoCard from "./ProductInfoCard";
import ProductSuggestionsTab from "./tabs/ProductSuggestionsTab";
import ProductFindAllTab from "./tabs/ProductFindAllTab";
import { toast } from "react-toastify";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";

// Type alias for dispatch
type AppDispatch = ThunkDispatch<RootState, any, UnknownAction>;

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const ProductDescDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const appDispatch: AppDispatch = useDispatch();

  // Extract PRDNO and VCID from URL parameters
  const urlParams = new URLSearchParams(location.search);
  const vcid = urlParams.get('VCID');
  const prdno = id; // The id in the URL path is the PRDNO

  const [value, setValue] = useState(0);
  const [selectedProduct, setSelectedProduct] = useState<{ VCID: number; PRDNO: string; DESCP?: string } | null>(null);
  const [isPending, setIsPending] = useState(false);
  const [isTabLoading, setIsTabLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // TODO: Add product-specific state from Redux store
  // const {
  //   productDetails,
  //   isLoading: isReduxLoading,
  //   error,
  //   productSuggestions,
  //   productSearchResults,
  //   productSuggestionsTotalCount,
  //   productSearchTotalCount,
  // } = useSelector((state: RootState) => state.product);

  const [suggestionsPagination, setSuggestionsPagination] = React.useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [findAllPagination, setFindAllPagination] = React.useState({
    page: 1,
    rowsPerPage: 10,
  });

  useEffect(() => {
    if (prdno && vcid) {
      // TODO: Fetch product details using VCID
      // appDispatch(getProductMappingDetails({ vcid: parseInt(vcid) }));
      setIsInitialLoading(false);
    }
  }, [prdno, vcid, appDispatch]);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    setIsTabLoading(true);

    // Simulate loading delay
    setTimeout(() => {
      setIsTabLoading(false);
    }, 500);
  };

  const handleSuggestionsPageChange = (newPage: number, newRowsPerPage: number) => {
    setSuggestionsPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const handleFindAllPageChange = (newPage: number, newRowsPerPage: number) => {
    setFindAllPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const handleProductSelect = (product: { VCID: number; PRDNO: string; DESCP?: string }) => {
    setSelectedProduct(product);
  };

  const handleSave = async () => {
    if (!selectedProduct || !vcid) return;

    setIsPending(true);
    try {
      const result = await appDispatch(
        updateProductMapping({
          VCID: vcid,
          PRDNO: selectedProduct.PRDNO,
          MappingUser: "test", // Static value until login feature is implemented
        }),
      );

      if (updateProductMapping.fulfilled.match(result)) {
        const response = result.payload;
        if (response.status === "success") {
          toast.success(response.message);
          // Update the mapping status in the left component with the selected product's DESCP
          // This will be handled by refreshing the product details
          setSelectedProduct(null); // Clear selection after successful save
        } else {
          toast.error("Something went wrong");
        }
      } else {
        toast.error("Something went wrong");
      }
    } catch (error: any) {
      console.error("Failed to save mapping:", error);
      toast.error("Something went wrong");
    } finally {
      setIsPending(false);
    }
  };

  const handleCancel = () => {
    // Deselect the currently selected product
    setSelectedProduct(null);
  };

  const handleMappedToClick = (mappedProduct: any) => {
    // Navigate to product details or handle as needed
    console.log("Mapped to clicked:", mappedProduct);
  };

  if (isInitialLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (!prdno || !vcid) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" color="error">
          Invalid product parameters. PRDNO and VCID are required.
        </Typography>
        <Button onClick={() => navigate(-1)} sx={{ mt: 2 }}>
          Go Back
        </Button>
      </Box>
    );
  }

  return (
    <div>
      <TopBarLayout breadcrumbItems={["Product", "Product Description"]} />

      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* Left side - Product Info Card */}
          <Grid item xs={12} md={6}>
            <ProductInfoCard
              vcid={vcid}
              prdno={prdno}
              onUnmap={() => {}} // Delete functionality will be implemented later
              isPending={isPending}
            />
          </Grid>

          {/* Right side - Tabs */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ height: "fit-content" }}>
              <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                <Tabs value={value} onChange={handleChange} aria-label="product tabs">
                  <Tab label="Suggestions" {...a11yProps(0)} />
                  <Tab label="Find All" {...a11yProps(1)} />
                </Tabs>
              </Box>

              <TabPanel value={value} index={0}>
                {isTabLoading ? (
                  <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <ProductSuggestionsTab
                    vcid={vcid}
                    onMappedToClick={handleMappedToClick}
                    onProductSelect={handleProductSelect}
                    selectedProduct={selectedProduct}
                    pagination={suggestionsPagination}
                    onPageChange={handleSuggestionsPageChange}
                  />
                )}
              </TabPanel>

              <TabPanel value={value} index={1}>
                {isTabLoading ? (
                  <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <ProductFindAllTab
                    vcid={vcid}
                    onMappedToClick={handleMappedToClick}
                    onProductSelect={handleProductSelect}
                    selectedProduct={selectedProduct}
                    pagination={findAllPagination}
                    onPageChange={handleFindAllPageChange}
                  />
                )}
              </TabPanel>

              {/* Save/Unmap buttons */}
              <Box sx={{ p: 2, display: "flex", gap: 2, justifyContent: "flex-end" }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSave}
                  disabled={!selectedProduct || isPending}
                  sx={{
                    opacity: !selectedProduct ? 0.5 : 1,
                    cursor: !selectedProduct ? 'not-allowed' : 'pointer'
                  }}
                >
                  {isPending ? <CircularProgress size={20} /> : "Save"}
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={handleCancel}
                  disabled={!selectedProduct || isPending}
                  sx={{
                    opacity: !selectedProduct ? 0.5 : 1,
                    cursor: !selectedProduct ? 'not-allowed' : 'pointer'
                  }}
                >
                  Cancel
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </div>
  );
};

export default ProductDescDetailsPage;
