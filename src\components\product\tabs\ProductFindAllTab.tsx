import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { Box, Radio, CircularProgress } from "@mui/material";
import TableComponent from "../../TableComponent";
import { searchProducts } from "../../../services/action/product.action";
import { ThunkDispatch } from "redux-thunk";
import { RootState } from "../../../store";
import { UnknownAction } from "@reduxjs/toolkit";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

interface ProductSearchResult {
  PRDNO: string;
  DESCP: string;
  ItemID: number;
  SkuName: string;
  AssociatedItem: string;
  BrandName: string;
  REUPC: string;
  GRUPC: string;
}

interface ProductSearchResponse {
  products: ProductSearchResult[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

interface ProductFindAllTabProps {
  vcid: string;
  onMappedToClick: (product: ProductSearchResult) => void;
  onProductSelect: (product: { VCID: number; PRDNO: string }) => void;
  selectedProduct: { VCID: number; PRDNO: string } | null;
  pagination: {
    page: number;
    rowsPerPage: number;
  };
  onPageChange: (newPage: number, newRowsPerPage: number) => void;
}

const ProductFindAllTab: React.FC<ProductFindAllTabProps> = ({
  vcid,
  onMappedToClick,
  onProductSelect,
  selectedProduct,
  pagination,
  onPageChange,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const [products, setProducts] = useState<ProductSearchResult[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch products data
  useEffect(() => {
    const fetchProducts = async () => {
      if (vcid) {
        setIsLoading(true);
        setError(null);

        try {
          // For Find All tab, we search all products without specific filters
          const params = {
            page: pagination.page,
            limit: pagination.rowsPerPage,
          };

          const result = await dispatch(searchProducts(params));

          if (searchProducts.fulfilled.match(result)) {
            setProducts(result.payload.products);
            setTotalCount(result.payload.total_count);
          } else {
            setError(result.payload || "Failed to fetch products");
          }
        } catch (err) {
          setError("Failed to fetch products");
          console.error("Error fetching products:", err);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchProducts();
  }, [vcid, pagination.page, pagination.rowsPerPage, dispatch]);

  const handleRadioChange = (product: ProductSearchResult) => {
    onProductSelect({
      VCID: parseInt(vcid),
      PRDNO: product.PRDNO,
    });
  };

  const columns = [
    {
      id: "action",
      label: "Action",
      description: "Select product",
      format: (value: any, row: ProductSearchResult) => (
        <Radio
          checked={selectedProduct?.PRDNO === row.PRDNO}
          onChange={() => handleRadioChange(row)}
          value={row.PRDNO}
          name="product-selection"
        />
      ),
    },
    {
      id: "PRDNO",
      label: "PRDNO",
      description: "Product number",
      format: (value: string, row: ProductSearchResult) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => onMappedToClick(row)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "DESCP",
      label: "DESCP",
      description: "Product description",
    },
    {
      id: "ItemID",
      label: "ItemID",
      description: "Item identifier",
    },
    {
      id: "SkuName",
      label: "SkuName",
      description: "SKU name",
    },
    {
      id: "AssociatedItem",
      label: "AssociatedItem",
      description: "Associated item",
    },
  ];

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: "center", color: "error.main" }}>
        {error}
      </Box>
    );
  }

  return (
    <Box>
      {isLoading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
          <CircularProgress />
        </Box>
      ) : (
        <TableComponent
          columns={columns}
          rows={products}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={totalCount}
          onPageChange={onPageChange}
          showActions={false}
          isLoading={isLoading}
        />
      )}
    </Box>
  );
};

export default ProductFindAllTab;
