import React, { useState, useEffect } from "react";
import { Box, Radio, CircularProgress } from "@mui/material";
import TableComponent from "../../TableComponent";

interface ProductSearchResult {
  PRDNO: string;
  DESCP: string;
  ItemID: number;
  SkuName: string;
  AssociatedItem: string;
  BrandName: string;
  REUPC: string;
  GRUPC: string;
}

interface ProductSearchResponse {
  products: ProductSearchResult[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

interface ProductFindAllTabProps {
  prdno: string;
  onMappedToClick: (product: ProductSearchResult) => void;
  onProductSelect: (product: { VCID: number; PRDNO: string }) => void;
  selectedProduct: { VCID: number; PRDNO: string } | null;
  pagination: {
    page: number;
    rowsPerPage: number;
  };
  onPageChange: (newPage: number, newRowsPerPage: number) => void;
}

const ProductFindAllTab: React.FC<ProductFindAllTabProps> = ({
  prdno,
  onMappedToClick,
  onProductSelect,
  selectedProduct,
  pagination,
  onPageChange,
}) => {
  const [products, setProducts] = useState<ProductSearchResult[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch products data
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // TODO: Replace with actual API call
        // const params = new URLSearchParams({
        //   PRDNO: prdno,
        //   page: pagination.page.toString(),
        //   limit: pagination.rowsPerPage.toString(),
        // });
        // const response = await fetch(`/products/search?${params}`);
        // const data: ProductSearchResponse = await response.json();
        
        // Mock data based on the provided sample response
        const mockData: ProductSearchResponse = {
          products: [
            {
              PRDNO: "10285014",
              DESCP: "SHEB PRP PATE SM 24/75 G",
              ItemID: 124170,
              SkuName: "SHEBA - Perfect Portions Cat Adult Salmon Loaf 24 / 2.65 oz",
              AssociatedItem: "SHEBA - Perfect Portions Cat Adult Salmon Loaf Multi Serve Flex Tray",
              BrandName: "SHEBA",
              REUPC: "10023100110216",
              GRUPC: "10023100110216"
            },
            // Add more mock data as needed
          ],
          total_count: 1,
          page: pagination.page,
          limit: pagination.rowsPerPage,
          has_next: false,
          has_previous: false
        };
        
        setProducts(mockData.products);
        setTotalCount(mockData.total_count);
      } catch (err) {
        setError("Failed to fetch products");
        console.error("Error fetching products:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (prdno) {
      fetchProducts();
    }
  }, [prdno, pagination.page, pagination.rowsPerPage]);

  const handleRadioChange = (product: ProductSearchResult) => {
    onProductSelect({
      VCID: 0, // VCID is not available in this context, will be handled by parent
      PRDNO: product.PRDNO,
    });
  };

  const columns = [
    {
      id: "action",
      label: "Action",
      description: "Select product",
      format: (value: any, row: ProductSearchResult) => (
        <Radio
          checked={selectedProduct?.PRDNO === row.PRDNO}
          onChange={() => handleRadioChange(row)}
          value={row.PRDNO}
          name="product-selection"
        />
      ),
    },
    {
      id: "PRDNO",
      label: "PRDNO",
      description: "Product number",
      format: (value: string, row: ProductSearchResult) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => onMappedToClick(row)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "DESCP",
      label: "DESCP",
      description: "Product description",
    },
    {
      id: "ItemID",
      label: "ItemID",
      description: "Item identifier",
    },
    {
      id: "SkuName",
      label: "SkuName",
      description: "SKU name",
    },
    {
      id: "AssociatedItem",
      label: "AssociatedItem",
      description: "Associated item",
    },
  ];

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: "center", color: "error.main" }}>
        {error}
      </Box>
    );
  }

  return (
    <Box>
      {isLoading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
          <CircularProgress />
        </Box>
      ) : (
        <TableComponent
          columns={columns}
          rows={products}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={totalCount}
          onPageChange={onPageChange}
          showActions={false}
          isLoading={isLoading}
        />
      )}
    </Box>
  );
};

export default ProductFindAllTab;
