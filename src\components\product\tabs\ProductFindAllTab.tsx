import React, { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { Box, Radio, CircularProgress } from "@mui/material";
import TableComponent from "../../TableComponent";
import FilterRow from "../../FilterRow";
import { searchProducts } from "../../../services/action/product.action";
import { ThunkDispatch } from "redux-thunk";
import { RootState } from "../../../store";
import { UnknownAction } from "@reduxjs/toolkit";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

interface ProductSearchResult {
  PRDNO: string;
  DESCP: string;
  ItemID: number;
  SkuName: string;
  AssociatedItem: string;
  BrandName: string;
  REUPC: string;
  GRUPC: string;
}

interface ProductSearchResponse {
  products: ProductSearchResult[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

interface ProductFindAllTabProps {
  vcid: string;
  onMappedToClick: (product: ProductSearchResult) => void;
  onProductSelect: (product: { VCID: number; PRDNO: string }) => void;
  selectedProduct: { VCID: number; PRDNO: string } | null;
  pagination: {
    page: number;
    rowsPerPage: number;
  };
  onPageChange: (newPage: number, newRowsPerPage: number) => void;
}

const ProductFindAllTab: React.FC<ProductFindAllTabProps> = ({
  vcid,
  onMappedToClick,
  onProductSelect,
  selectedProduct,
  pagination,
  onPageChange,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const [products, setProducts] = useState<ProductSearchResult[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filter state
  const [filters, setFilters] = useState({
    PRDNO: "",
    SalesDesc: "", // This will be sent as SkuName to API
    BrandName: "",
    RetailUPC: ""
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);
  const filtersRef = useRef(filters);

  // Filter handlers
  const handleFilterChange = (name: string, value: string) => {
    setFilters(prev => ({ ...prev, [name]: value }));
    filtersRef.current = { ...filtersRef.current, [name]: value };
  };

  const handleApplyFilters = () => {
    setIsFilterApplied(true);
    onPageChange(1, pagination.rowsPerPage); // Reset to first page
  };

  const handleResetFilters = () => {
    setFilters({
      PRDNO: "",
      SalesDesc: "",
      BrandName: "",
      RetailUPC: ""
    });
    filtersRef.current = {
      PRDNO: "",
      SalesDesc: "",
      BrandName: "",
      RetailUPC: ""
    };
    setIsFilterApplied(false);
    setProducts([]);
    setTotalCount(0);
  };

  // Fetch products data
  useEffect(() => {
    const fetchProducts = async () => {
      if (vcid && isFilterApplied) {
        setIsLoading(true);
        setError(null);

        try {
          // Map SalesDesc to SkuName for API
          const params = {
            page: pagination.page,
            limit: pagination.rowsPerPage,
            PRDNO: filtersRef.current.PRDNO || undefined,
            SkuName: filtersRef.current.SalesDesc || undefined, // Sales Desc maps to SkuName
            BrandName: filtersRef.current.BrandName || undefined,
            RetailUPC: filtersRef.current.RetailUPC || undefined,
          };

          const result = await dispatch(searchProducts(params));

          if (searchProducts.fulfilled.match(result)) {
            setProducts(result.payload.products);
            setTotalCount(result.payload.total_count);
          } else {
            setError(result.payload || "Failed to fetch products");
          }
        } catch (err) {
          setError("Failed to fetch products");
          console.error("Error fetching products:", err);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchProducts();
  }, [vcid, pagination.page, pagination.rowsPerPage, isFilterApplied, dispatch]);

  const handleRadioChange = (product: ProductSearchResult) => {
    onProductSelect({
      VCID: parseInt(vcid),
      PRDNO: product.PRDNO,
    });
  };

  // Filter configurations
  const filterConfigs = [
    {
      name: "PRDNO",
      label: "Prd No",
      type: "text" as const,
      value: filters.PRDNO,
      onChange: (value: string) => handleFilterChange("PRDNO", value),
    },
    {
      name: "SalesDesc",
      label: "Sales Desc",
      type: "text" as const,
      value: filters.SalesDesc,
      onChange: (value: string) => handleFilterChange("SalesDesc", value),
    },
    {
      name: "BrandName",
      label: "Brand",
      type: "text" as const,
      value: filters.BrandName,
      onChange: (value: string) => handleFilterChange("BrandName", value),
    },
    {
      name: "RetailUPC",
      label: "UPC",
      type: "text" as const,
      value: filters.RetailUPC,
      onChange: (value: string) => handleFilterChange("RetailUPC", value),
    },
  ];

  const columns = [
    {
      id: "action",
      label: "Action",
      description: "Select product",
      format: (value: any, row: ProductSearchResult) => (
        <Radio
          checked={selectedProduct?.PRDNO === row.PRDNO}
          onChange={() => handleRadioChange(row)}
          value={row.PRDNO}
          name="product-selection"
        />
      ),
    },
    {
      id: "PRDNO",
      label: "PRDNO",
      description: "Product number",
      format: (value: string, row: ProductSearchResult) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => onMappedToClick(row)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "DESCP",
      label: "DESCP",
      description: "Product description",
    },
    {
      id: "ItemID",
      label: "ItemID",
      description: "Item identifier",
    },
    {
      id: "SkuName",
      label: "SkuName",
      description: "SKU name",
    },
    {
      id: "AssociatedItem",
      label: "AssociatedItem",
      description: "Associated item",
    },
  ];

  return (
    <Box>
      {/* Filter Row */}
      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {/* Table - only show when filters are applied */}
      {isFilterApplied && (
        <>
          {isLoading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Box sx={{ p: 2, textAlign: "center", color: "error.main" }}>
              {error}
            </Box>
          ) : (
            <TableComponent
              columns={columns}
              rows={products}
              rowsPerPage={pagination.rowsPerPage}
              page={pagination.page}
              totalCount={totalCount}
              onPageChange={onPageChange}
              showActions={false}
              isLoading={isLoading}
            />
          )}
        </>
      )}
    </Box>
  );
};

export default ProductFindAllTab;
