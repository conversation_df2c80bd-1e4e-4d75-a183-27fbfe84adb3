import { createAsyncThunk } from '@reduxjs/toolkit';
import {
  productService,
  ProductSearchParams,
  ProductSearchResponse,
  ListItemsResponse,
  ProductMappingDetails,
  ProductMappingSuggestionsParams,
  ProductMappingSuggestionsResponse,
  ProductMappingUpdateParams,
  ProductMappingUpdateResponse
} from '../api/product.service';

// Async thunk for fetching list items
export const fetchListItems = createAsyncThunk<
  string[],
  void,
  { rejectValue: string }
>(
  'product/fetchListItems',
  async (_, { rejectWithValue }) => {
    try {
      const response = await productService.getListItems();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch list items');
    }
  }
);

// Async thunk for searching products
export const searchProducts = createAsyncThunk<
  ProductSearchResponse,
  ProductSearchParams,
  { rejectValue: string }
>(
  'product/searchProducts',
  async (params, { rejectWithValue }) => {
    try {
      const response = await productService.searchProducts(params);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to search products');
    }
  }
);

// Async thunk for fetching product by ID
export const fetchProductById = createAsyncThunk<
  any,
  string,
  { rejectValue: string }
>(
  'product/fetchProductById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await productService.getProductById(id);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch product');
    }
  }
);

// Async thunk for fetching product mapping details
export const fetchProductMappingDetails = createAsyncThunk<
  ProductMappingDetails,
  number,
  { rejectValue: string }
>(
  'product/fetchProductMappingDetails',
  async (vcid, { rejectWithValue }) => {
    try {
      const response = await productService.getProductMappingDetails(vcid);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch product mapping details');
    }
  }
);

// Async thunk for fetching product mapping suggestions
export const fetchProductMappingSuggestions = createAsyncThunk<
  ProductMappingSuggestionsResponse,
  ProductMappingSuggestionsParams,
  { rejectValue: string }
>(
  'product/fetchProductMappingSuggestions',
  async (params, { rejectWithValue }) => {
    try {
      const response = await productService.getProductMappingSuggestions(params);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch product mapping suggestions');
    }
  }
);

// Async thunk for updating product mapping
export const updateProductMapping = createAsyncThunk<
  ProductMappingUpdateResponse,
  ProductMappingUpdateParams,
  { rejectValue: string }
>(
  'product/updateProductMapping',
  async (params, { rejectWithValue }) => {
    try {
      const response = await productService.updateProductMapping(params);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update product mapping');
    }
  }
);
