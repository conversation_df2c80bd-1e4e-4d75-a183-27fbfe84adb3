import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Paper,
  Typography,
  Divider,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  FormControlLabel,
  CircularProgress,
  Alert
} from "@mui/material";
import styles from "../../styles/StoreInfoCard.module.css";
import { RootState } from "../../store";
import { fetchProductMappingDetails } from "../../services/action/product.action";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

interface ProductInfo {
  VCID: number;
  PRDNO: string;
  FromPRDNO: string | null;
  ToPRDNO: string | null;
  DataSource: string;
  DistItemID: string;
  DistItemNum: string;
  DistItemUPC: string;
  DistItemName: string;
  LastTransactionDate: string;
  L4POSSales: number;
  IsPending: boolean;
  MappingStatus: string | null;
}

interface ProductInfoCardProps {
  vcid: string;
  prdno: string;
  onUnmap: () => void;
  isPending: boolean;
}

const ProductInfoCard: React.FC<ProductInfoCardProps> = ({
  vcid,
  prdno,
  onUnmap,
  isPending,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const [productInfo, setProductInfo] = useState<ProductInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch product details using Redux action
  useEffect(() => {
    const fetchProductDetails = async () => {
      if (vcid) {
        setIsLoading(true);
        setError(null);
        try {
          const result = await dispatch(fetchProductMappingDetails(parseInt(vcid)));
          if (fetchProductMappingDetails.fulfilled.match(result)) {
            setProductInfo(result.payload);
          } else {
            setError(result.payload || "Failed to fetch product details");
          }
        } catch (err) {
          setError("Failed to fetch product details");
          console.error("Error fetching product details:", err);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchProductDetails();
  }, [vcid, prdno]);

  if (isLoading) {
    return (
      <Paper elevation={3} className={styles.card}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
          <CircularProgress />
        </Box>
      </Paper>
    );
  }

  if (error || !productInfo) {
    return (
      <Paper elevation={3} className={styles.card}>
        <Typography variant="h6" color="error">
          {error || "Product information not available"}
        </Typography>
      </Paper>
    );
  }

  // Table data for QTY, POS $, POS EA, GSV, GSV EA
  const tableData = [
    {
      period: "Last 4 Wk:",
      qty: "-",
      posAmount: productInfo.L4POSSales || "-",
      posEa: "-",
      gsv: "-",
      gsvEa: "-"
    },
    {
      period: "Last 52 Wk:",
      qty: "-",
      posAmount: "-",
      posEa: "-",
      gsv: "-",
      gsvEa: "-"
    },
    {
      period: "2010 On:",
      qty: "-",
      posAmount: "-",
      posEa: "-",
      gsv: "-",
      gsvEa: "-"
    }
  ];

  return (
    <Paper elevation={3} className={styles.card}>
      <Typography variant="h6" className={styles.title}>
        Product Details
      </Typography>
      <Divider className={styles.divider} />

      {/* Product Information */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6}>
          <Box className={styles.infoSection}>
            <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
              Data Source
            </Typography>
            <Typography variant="body1">{productInfo.DataSource || "-"}</Typography>
          </Box>
        </Grid>
        <Grid item xs={6}>
          <Box className={styles.infoSection}>
            <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
              Item Name
            </Typography>
            <Typography variant="body1">{productInfo.DistItemName || "-"}</Typography>
          </Box>
        </Grid>
        <Grid item xs={6}>
          <Box className={styles.infoSection}>
            <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
              Data Item ID
            </Typography>
            <Typography variant="body1">{productInfo.DistItemID || "-"}</Typography>
          </Box>
        </Grid>
        <Grid item xs={6}>
          <Box className={styles.infoSection}>
            <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
              Dist UPC
            </Typography>
            <Typography variant="body1">{productInfo.DistItemUPC || "-"}</Typography>
          </Box>
        </Grid>
      </Grid>

      {/* Tabular Data */}
      <TableContainer component={Paper} variant="outlined" sx={{ mb: 3 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell></TableCell>
              <TableCell align="center"><strong>QTY</strong></TableCell>
              <TableCell align="center"><strong>POS $</strong></TableCell>
              <TableCell align="center"><strong>POS EA</strong></TableCell>
              <TableCell align="center"><strong>GSV</strong></TableCell>
              <TableCell align="center"><strong>GSV EA</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tableData.map((row, index) => (
              <TableRow key={index}>
                <TableCell component="th" scope="row">
                  <strong>{row.period}</strong>
                </TableCell>
                <TableCell align="center">{row.qty}</TableCell>
                <TableCell align="center">{row.posAmount}</TableCell>
                <TableCell align="center">{row.posEa}</TableCell>
                <TableCell align="center">{row.gsv}</TableCell>
                <TableCell align="center">{row.gsvEa}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Checkboxes */}
      <Box sx={{ mb: 3 }}>
        <FormControlLabel
          control={<Checkbox />}
          label="Insufficient Item ID/UPC"
        />
        <FormControlLabel
          control={<Checkbox />}
          label="Insufficient Item Name"
        />
      </Box>

      {/* Status section */}
      <Box
        sx={{
          p: 2,
          backgroundColor: "#f5f5f5",
          borderRadius: 1,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Box>
          <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
            Mapping Status
          </Typography>
          <Typography variant="body1">{productInfo.MappingStatus || "-"}</Typography>
        </Box>
        <Box>
          <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
            Is Pending
          </Typography>
          <Typography variant="body1">{productInfo.IsPending ? "Yes" : "No"}</Typography>
        </Box>
      </Box>
    </Paper>
  );
};

export default ProductInfoCard;
