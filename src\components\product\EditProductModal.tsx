import React, { useState } from "react";
import {
  Modal,
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Stack,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const style = {
  position: "absolute" as "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: { xs: "90%", sm: "80%", md: "60%" },
  bgcolor: "background.paper",
  boxShadow: 24,
  p: 4,
  borderRadius: 2,
  maxHeight: "90vh",
  overflowY: "auto",
};

interface ProductInfo {
  VCID: number;
  PRDNO: string;
  FromPRDNO: string | null;
  ToPRDNO: string | null;
  DataSource: string;
  DistItemID: string;
  DistItemNum: string;
  DistItemUPC: string;
  DistItemName: string;
  LastTransactionDate: string;
  L4POSSales: number;
  IsPending: boolean;
  MappingStatus: string | null;
}

interface EditProductModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: (updatedInfo: Partial<ProductInfo>) => void;
  productInfo: ProductInfo;
}

const EditProductModal: React.FC<EditProductModalProps> = ({ 
  open, 
  onClose, 
  productInfo, 
  onSuccess 
}) => {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    dataSource: productInfo.DataSource || "",
    itemName: productInfo.DistItemName || "",
    dataItemId: productInfo.DistItemID || "",
    distUpc: productInfo.DistItemUPC || "",
    firstTrans: productInfo.LastTransactionDate || "",
    last4WeekPos: productInfo.L4POSSales?.toString() || "0",
    last52WeekPos: "0",
    last52WeekGsv: "0",
    last4WeekGsv: "0",
    total2010On: "0",
    isPending: productInfo.IsPending || false,
    mappingStatus: productInfo.MappingStatus || "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Implement actual API call to update product details
      // For now, just simulate success
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success("Product information updated successfully!");

      // Call the success callback with updated info if provided
      if (onSuccess) {
        onSuccess({
          DataSource: formData.dataSource,
          DistItemName: formData.itemName,
          DistItemID: formData.dataItemId,
          DistItemUPC: formData.distUpc,
          LastTransactionDate: formData.firstTrans,
          L4POSSales: parseFloat(formData.last4WeekPos) || 0,
          IsPending: formData.isPending,
          MappingStatus: formData.mappingStatus,
        });
      }

      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to update product information");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box sx={style}>
        <Typography variant="h5" component="h2" gutterBottom>
          Edit Product Details
        </Typography>
        
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Data Source"
                name="dataSource"
                value={formData.dataSource}
                onChange={handleChange}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Data Item ID"
                name="dataItemId"
                value={formData.dataItemId}
                onChange={handleChange}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Item Name"
                name="itemName"
                value={formData.itemName}
                onChange={handleChange}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Dist UPC"
                name="distUpc"
                value={formData.distUpc}
                onChange={handleChange}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Trans"
                name="firstTrans"
                value={formData.firstTrans}
                onChange={handleChange}
                variant="outlined"
                type="date"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last 4 week POS"
                name="last4WeekPos"
                value={formData.last4WeekPos}
                onChange={handleChange}
                variant="outlined"
                type="number"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last 52 Wk GSV"
                name="last52WeekGsv"
                value={formData.last52WeekGsv}
                onChange={handleChange}
                variant="outlined"
                type="number"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="2010 On"
                name="total2010On"
                value={formData.total2010On}
                onChange={handleChange}
                variant="outlined"
                type="number"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.isPending}
                    onChange={handleChange}
                    name="isPending"
                  />
                }
                label="Is Pending"
              />
            </Grid>
          </Grid>

          <Stack direction="row" spacing={2} sx={{ mt: 3, justifyContent: "flex-end" }}>
            <Button onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={20} /> : null}
            >
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </Stack>
        </form>
      </Box>
    </Modal>
  );
};

export default EditProductModal;
