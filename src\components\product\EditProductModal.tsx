import React, { useState } from "react";
import {
  Modal,
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Stack,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const style = {
  position: "absolute" as "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: { xs: "90%", sm: "80%", md: "60%" },
  bgcolor: "background.paper",
  boxShadow: 24,
  p: 4,
  borderRadius: 2,
  maxHeight: "90vh",
  overflowY: "auto",
};

interface ProductInfo {
  VCID: number;
  PRDNO: string;
  FromPRDNO: string | null;
  ToPRDNO: string | null;
  DataSource: string;
  DistItemID: string;
  DistItemNum: string;
  DistItemUPC: string;
  DistItemName: string;
  LastTransactionDate: string;
  L4POSSales: number;
  IsPending: boolean;
  MappingStatus: string | null;
}

interface EditProductModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: (updatedInfo: Partial<ProductInfo>) => void;
  productInfo: ProductInfo;
}

const EditProductModal: React.FC<EditProductModalProps> = ({ 
  open, 
  onClose, 
  productInfo, 
  onSuccess 
}) => {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    // Editable fields
    itemName: productInfo.DistItemName || "",
    distUpc: productInfo.DistItemUPC || "",

    // Checkbox fields
    isPending: productInfo.IsPending || false,
    removeInsufficientItemIdUpc: (productInfo as any)["remove Insufficient Item ID/UPC"] || false,
    insufficientItemName: (productInfo as any)["Insufficient Item Name"] || false,

    // Static fields (for display only)
    dataSource: productInfo.DataSource || "",
    dataItemId: productInfo.DistItemID || "",
    mappingStatus: productInfo.MappingStatus || "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Prepare payload for products/update-product-details API
      const payload = {
        item_name: formData.itemName,
        dist_upc: formData.distUpc,
        is_pending: formData.isPending,
        remove_insufficient_item_id_upc: formData.removeInsufficientItemIdUpc,
        insufficient_item_name: formData.insufficientItemName,
      };

      // TODO: Replace with actual API call to products/update-product-details
      console.log("Sending payload to products/update-product-details:", payload);
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success("Product information updated successfully!");

      // Call the success callback with updated info if provided
      if (onSuccess) {
        onSuccess({
          DistItemName: formData.itemName,
          DistItemUPC: formData.distUpc,
          IsPending: formData.isPending,
        });
      }

      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to update product information");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box sx={style}>
        <Typography variant="h5" component="h2" gutterBottom>
          Edit Product Details
        </Typography>
        
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Static Fields */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Data Source"
                name="dataSource"
                value={formData.dataSource}
                variant="outlined"
                disabled
                InputProps={{
                  readOnly: true,
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Data Item ID"
                name="dataItemId"
                value={formData.dataItemId}
                variant="outlined"
                disabled
                InputProps={{
                  readOnly: true,
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Mapping Status"
                name="mappingStatus"
                value={formData.mappingStatus}
                variant="outlined"
                disabled
                InputProps={{
                  readOnly: true,
                }}
              />
            </Grid>

            {/* Editable Fields */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Item Name"
                name="itemName"
                value={formData.itemName}
                onChange={handleChange}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Dist UPC"
                name="distUpc"
                value={formData.distUpc}
                onChange={handleChange}
                variant="outlined"
              />
            </Grid>

            {/* Checkbox Fields */}
            <Grid item xs={12} sm={4}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.isPending}
                    onChange={handleChange}
                    name="isPending"
                  />
                }
                label="Is Pending"
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.removeInsufficientItemIdUpc}
                    onChange={handleChange}
                    name="removeInsufficientItemIdUpc"
                  />
                }
                label="Remove Insufficient Item ID/UPC"
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.insufficientItemName}
                    onChange={handleChange}
                    name="insufficientItemName"
                  />
                }
                label="Insufficient Item Name"
              />
            </Grid>
          </Grid>

          <Stack direction="row" spacing={2} sx={{ mt: 3, justifyContent: "flex-end" }}>
            <Button onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={20} /> : null}
            >
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </Stack>
        </form>
      </Box>
    </Modal>
  );
};

export default EditProductModal;
